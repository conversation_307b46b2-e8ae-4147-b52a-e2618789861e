#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/eslint-config-prettier@10.1.5_eslint@9.27.0_jiti@2.4.2_/node_modules/eslint-config-prettier/bin/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/eslint-config-prettier@10.1.5_eslint@9.27.0_jiti@2.4.2_/node_modules/eslint-config-prettier/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/eslint-config-prettier@10.1.5_eslint@9.27.0_jiti@2.4.2_/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/eslint-config-prettier@10.1.5_eslint@9.27.0_jiti@2.4.2_/node_modules/eslint-config-prettier/bin/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/eslint-config-prettier@10.1.5_eslint@9.27.0_jiti@2.4.2_/node_modules/eslint-config-prettier/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/eslint-config-prettier@10.1.5_eslint@9.27.0_jiti@2.4.2_/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../eslint-config-prettier/bin/cli.js" "$@"
else
  exec node  "$basedir/../eslint-config-prettier/bin/cli.js" "$@"
fi
