{"name": "@cubent/cloud", "description": "cubent Cloud VSCode integration.", "version": "0.0.0", "type": "module", "exports": "./src/index.ts", "scripts": {"lint": "eslint src --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "test": "vitest run", "clean": "rimraf dist .turbo"}, "dependencies": {"@cubent/telemetry": "workspace:^", "@cubent/types": "workspace:^", "axios": "^1.7.4", "zod": "^3.24.2"}, "devDependencies": {"@cubent/config-eslint": "workspace:^", "@cubent/config-typescript": "workspace:^", "@types/node": "^22.15.20", "@types/vscode": "^1.84.0", "vitest": "^3.1.3"}}