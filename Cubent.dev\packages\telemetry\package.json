{"name": "@cubent/telemetry", "description": "cubent telemetry service and clients.", "version": "0.0.0", "type": "module", "exports": "./src/index.ts", "scripts": {"lint": "eslint src --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "test": "vitest run", "clean": "rimraf dist .turbo"}, "dependencies": {"@cubent/types": "workspace:^", "posthog-node": "^4.7.0", "zod": "^3.24.2"}, "devDependencies": {"@cubent/config-eslint": "workspace:^", "@cubent/config-typescript": "workspace:^", "@types/node": "^22.15.20", "@types/vscode": "^1.84.0", "vitest": "^3.1.3"}}