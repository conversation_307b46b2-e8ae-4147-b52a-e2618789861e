-- User Management Schema for Cubent
-- This schema supports user profiles, subscriptions, usage tracking, and trial management

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Subscription tiers enum
CREATE TYPE subscription_tier AS ENUM (
    'free_trial',
    'basic',
    'pro',
    'enterprise'
);

-- Subscription status enum
CREATE TYPE subscription_status AS ENUM (
    'active',
    'trial',
    'expired',
    'cancelled',
    'suspended'
);

-- Usage alert types
CREATE TYPE usage_alert_type AS ENUM (
    'token_limit',
    'cost_limit',
    'request_limit',
    'trial_expiry'
);

-- Alert severity levels
CREATE TYPE alert_severity AS ENUM (
    'info',
    'warning',
    'critical'
);

-- User profiles table
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clerk_user_id TEXT UNIQUE NOT NULL, -- Link to Clerk user
    email TEXT NOT NULL,
    name TEXT,
    picture TEXT,
    
    -- Subscription information
    subscription_tier subscription_tier NOT NULL DEFAULT 'free_trial',
    subscription_status subscription_status NOT NULL DEFAULT 'trial',
    subscription_start_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    subscription_end_date TIMESTAMPTZ,
    
    -- Trial information
    trial_start_date TIMESTAMPTZ,
    trial_end_date TIMESTAMPTZ,
    trial_extensions INTEGER NOT NULL DEFAULT 0,
    
    -- User preferences (stored as JSONB for flexibility)
    preferences JSONB NOT NULL DEFAULT '{
        "usageWarningsEnabled": true,
        "trialExpiryNotifications": true,
        "detailedUsageTracking": true,
        "costAlertsEnabled": true,
        "costAlertThreshold": 80,
        "autoUpgradeEnabled": false,
        "preferredUpgradeTier": "basic"
    }',
    
    -- Metadata
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_active_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Indexes
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Usage quotas table (defines limits for each subscription tier)
CREATE TABLE usage_quotas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscription_tier subscription_tier NOT NULL UNIQUE,
    
    -- Token and cost limits
    monthly_token_limit BIGINT NOT NULL,
    monthly_cost_limit DECIMAL(10,2) NOT NULL,
    
    -- Request rate limits
    hourly_request_limit INTEGER NOT NULL,
    daily_request_limit INTEGER NOT NULL,
    
    -- Feature limits
    max_context_window INTEGER NOT NULL,
    allowed_models TEXT[] NOT NULL DEFAULT '{}',
    
    -- Feature flags
    can_use_reasoning_models BOOLEAN NOT NULL DEFAULT FALSE,
    can_use_codebase_index BOOLEAN NOT NULL DEFAULT FALSE,
    can_use_custom_modes BOOLEAN NOT NULL DEFAULT FALSE,
    can_export_history BOOLEAN NOT NULL DEFAULT FALSE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Usage metrics table (tracks actual usage per user)
CREATE TABLE usage_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    -- Current period usage
    current_month_tokens BIGINT NOT NULL DEFAULT 0,
    current_month_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    current_hour_requests INTEGER NOT NULL DEFAULT 0,
    current_day_requests INTEGER NOT NULL DEFAULT 0,
    
    -- Historical totals
    total_tokens_used BIGINT NOT NULL DEFAULT 0,
    total_cost_accrued DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_requests_made BIGINT NOT NULL DEFAULT 0,
    
    -- Reset timestamps
    last_monthly_reset TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_hourly_reset TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_daily_reset TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Metadata
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- Model usage breakdown table
CREATE TABLE model_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    model_id TEXT NOT NULL,
    
    -- Usage stats for this model
    tokens_used BIGINT NOT NULL DEFAULT 0,
    cost_accrued DECIMAL(10,2) NOT NULL DEFAULT 0,
    requests_made INTEGER NOT NULL DEFAULT 0,
    
    -- Time period (monthly tracking)
    usage_month INTEGER NOT NULL, -- 1-12
    usage_year INTEGER NOT NULL,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(user_id, model_id, usage_month, usage_year)
);

-- Usage alerts table
CREATE TABLE usage_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    alert_type usage_alert_type NOT NULL,
    severity alert_severity NOT NULL,
    message TEXT NOT NULL,
    threshold DECIMAL(5,2) NOT NULL, -- Percentage threshold that triggered alert
    current_value DECIMAL(10,2) NOT NULL, -- Current usage value
    
    acknowledged BOOLEAN NOT NULL DEFAULT FALSE,
    acknowledged_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- API usage logs table (detailed tracking)
CREATE TABLE api_usage_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,

    -- Request details
    model_id TEXT NOT NULL,
    provider TEXT NOT NULL,

    -- Usage metrics
    input_tokens INTEGER,
    output_tokens INTEGER,
    total_tokens INTEGER,
    cost DECIMAL(8,4),

    -- Request metadata
    request_type TEXT, -- 'completion', 'chat', 'embedding', etc.
    context_size INTEGER,
    response_time_ms INTEGER,

    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Subscription history table
CREATE TABLE subscription_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    -- Subscription change details
    from_tier subscription_tier,
    to_tier subscription_tier NOT NULL,
    change_reason TEXT, -- 'upgrade', 'downgrade', 'trial_start', 'trial_end', etc.
    
    -- Billing information
    amount_paid DECIMAL(10,2),
    billing_period_start TIMESTAMPTZ,
    billing_period_end TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Insert default quota configurations
INSERT INTO usage_quotas (subscription_tier, monthly_token_limit, monthly_cost_limit, hourly_request_limit, daily_request_limit, max_context_window, allowed_models, can_use_reasoning_models, can_use_codebase_index, can_use_custom_modes, can_export_history) VALUES
('free_trial', 100000, 10.00, 50, 500, 32000, ARRAY['Claude 3.5 Sonnet', 'GPT-4o Mini', 'Gemini 1.5 Flash'], FALSE, FALSE, FALSE, FALSE),
('basic', 1000000, 50.00, 200, 2000, 128000, ARRAY['Claude 3.5 Sonnet', 'Claude Sonnet 4', 'GPT-4o', 'GPT-4o Mini', 'Gemini 1.5 Pro', 'Gemini 1.5 Flash'], FALSE, TRUE, TRUE, TRUE),
('pro', 5000000, 200.00, 500, 5000, 200000, ARRAY['Claude 3.5 Sonnet', 'Claude Sonnet 4', 'Claude 3.7 Sonnet (Thinking)', 'GPT-4o', 'GPT-4o Mini', 'o1-preview', 'o1-mini', 'Gemini 1.5 Pro', 'Gemini 2.0 Pro', 'DeepSeek V3'], TRUE, TRUE, TRUE, TRUE),
('enterprise', 20000000, 1000.00, 2000, 20000, 1000000, ARRAY[]::TEXT[], TRUE, TRUE, TRUE, TRUE);

-- Create indexes for better performance
CREATE INDEX idx_user_profiles_clerk_id ON user_profiles(clerk_user_id);
CREATE INDEX idx_user_profiles_email ON user_profiles(email);
CREATE INDEX idx_user_profiles_subscription ON user_profiles(subscription_tier, subscription_status);
CREATE INDEX idx_user_profiles_trial_end ON user_profiles(trial_end_date) WHERE trial_end_date IS NOT NULL;

CREATE INDEX idx_usage_metrics_user ON usage_metrics(user_id);
CREATE INDEX idx_model_usage_user_period ON model_usage(user_id, usage_year, usage_month);
CREATE INDEX idx_usage_alerts_user_unack ON usage_alerts(user_id) WHERE acknowledged = FALSE;

-- Create indexes for API usage logs table
CREATE INDEX idx_api_usage_user_date ON api_usage_logs(user_id, created_at);
CREATE INDEX idx_api_usage_model ON api_usage_logs(model_id);
CREATE INDEX idx_api_usage_date ON api_usage_logs(created_at);

-- Create functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_usage_quotas_updated_at BEFORE UPDATE ON usage_quotas FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_usage_metrics_updated_at BEFORE UPDATE ON usage_metrics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_model_usage_updated_at BEFORE UPDATE ON model_usage FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to reset usage counters
CREATE OR REPLACE FUNCTION reset_usage_counters()
RETURNS void AS $$
BEGIN
    -- Reset monthly counters (run on 1st of each month)
    UPDATE usage_metrics 
    SET 
        current_month_tokens = 0,
        current_month_cost = 0,
        last_monthly_reset = NOW()
    WHERE DATE_TRUNC('month', last_monthly_reset) < DATE_TRUNC('month', NOW());
    
    -- Reset hourly counters
    UPDATE usage_metrics 
    SET 
        current_hour_requests = 0,
        last_hourly_reset = NOW()
    WHERE last_hourly_reset < NOW() - INTERVAL '1 hour';
    
    -- Reset daily counters
    UPDATE usage_metrics 
    SET 
        current_day_requests = 0,
        last_daily_reset = NOW()
    WHERE DATE_TRUNC('day', last_daily_reset) < DATE_TRUNC('day', NOW());
END;
$$ LANGUAGE plpgsql;

-- Function to check trial expiry
CREATE OR REPLACE FUNCTION check_trial_expiry()
RETURNS void AS $$
BEGIN
    -- Update expired trials
    UPDATE user_profiles 
    SET 
        subscription_status = 'expired',
        updated_at = NOW()
    WHERE 
        subscription_status = 'trial' 
        AND trial_end_date IS NOT NULL 
        AND trial_end_date < NOW();
END;
$$ LANGUAGE plpgsql;

-- Row Level Security (RLS) policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE model_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_usage_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_history ENABLE ROW LEVEL SECURITY;

-- RLS policies (users can only access their own data)
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (clerk_user_id = auth.jwt() ->> 'sub');
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (clerk_user_id = auth.jwt() ->> 'sub');

CREATE POLICY "Users can view own usage" ON usage_metrics FOR SELECT USING (user_id IN (SELECT id FROM user_profiles WHERE clerk_user_id = auth.jwt() ->> 'sub'));
CREATE POLICY "Users can view own model usage" ON model_usage FOR SELECT USING (user_id IN (SELECT id FROM user_profiles WHERE clerk_user_id = auth.jwt() ->> 'sub'));
CREATE POLICY "Users can view own alerts" ON usage_alerts FOR SELECT USING (user_id IN (SELECT id FROM user_profiles WHERE clerk_user_id = auth.jwt() ->> 'sub'));
CREATE POLICY "Users can view own api logs" ON api_usage_logs FOR SELECT USING (user_id IN (SELECT id FROM user_profiles WHERE clerk_user_id = auth.jwt() ->> 'sub'));
CREATE POLICY "Users can view own subscription history" ON subscription_history FOR SELECT USING (user_id IN (SELECT id FROM user_profiles WHERE clerk_user_id = auth.jwt() ->> 'sub'));

-- Function to update usage metrics atomically
CREATE OR REPLACE FUNCTION update_usage_metrics(
    p_user_id UUID,
    p_tokens BIGINT,
    p_cost DECIMAL(10,2),
    p_requests INTEGER
)
RETURNS void AS $$
BEGIN
    -- Reset counters if needed
    PERFORM reset_usage_counters();

    -- Update usage metrics
    UPDATE usage_metrics
    SET
        current_month_tokens = current_month_tokens + p_tokens,
        current_month_cost = current_month_cost + p_cost,
        current_hour_requests = current_hour_requests + p_requests,
        current_day_requests = current_day_requests + p_requests,
        total_tokens_used = total_tokens_used + p_tokens,
        total_cost_accrued = total_cost_accrued + p_cost,
        total_requests_made = total_requests_made + p_requests,
        updated_at = NOW()
    WHERE user_id = p_user_id;

    -- If no row exists, create one
    IF NOT FOUND THEN
        INSERT INTO usage_metrics (
            user_id,
            current_month_tokens,
            current_month_cost,
            current_hour_requests,
            current_day_requests,
            total_tokens_used,
            total_cost_accrued,
            total_requests_made
        ) VALUES (
            p_user_id,
            p_tokens,
            p_cost,
            p_requests,
            p_requests,
            p_tokens,
            p_cost,
            p_requests
        );
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to get user usage summary
CREATE OR REPLACE FUNCTION get_user_usage_summary(p_user_id UUID)
RETURNS TABLE (
    current_month_tokens BIGINT,
    current_month_cost DECIMAL(10,2),
    monthly_token_limit BIGINT,
    monthly_cost_limit DECIMAL(10,2),
    token_percentage DECIMAL(5,2),
    cost_percentage DECIMAL(5,2),
    subscription_tier TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        um.current_month_tokens,
        um.current_month_cost,
        uq.monthly_token_limit,
        uq.monthly_cost_limit,
        CASE
            WHEN uq.monthly_token_limit > 0 THEN (um.current_month_tokens::DECIMAL / uq.monthly_token_limit * 100)
            ELSE 0
        END as token_percentage,
        CASE
            WHEN uq.monthly_cost_limit > 0 THEN (um.current_month_cost / uq.monthly_cost_limit * 100)
            ELSE 0
        END as cost_percentage,
        up.subscription_tier::TEXT
    FROM usage_metrics um
    JOIN user_profiles up ON um.user_id = up.id
    JOIN usage_quotas uq ON up.subscription_tier = uq.subscription_tier
    WHERE um.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON user_profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE ON usage_metrics TO authenticated;
GRANT SELECT, INSERT, UPDATE ON model_usage TO authenticated;
GRANT SELECT, INSERT, UPDATE ON usage_alerts TO authenticated;
GRANT SELECT, INSERT ON api_usage_logs TO authenticated;
GRANT SELECT, INSERT ON subscription_history TO authenticated;
GRANT SELECT ON usage_quotas TO authenticated;
GRANT EXECUTE ON FUNCTION update_usage_metrics TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_usage_summary TO authenticated;
GRANT EXECUTE ON FUNCTION reset_usage_counters TO authenticated;
GRANT EXECUTE ON FUNCTION check_trial_expiry TO authenticated;
